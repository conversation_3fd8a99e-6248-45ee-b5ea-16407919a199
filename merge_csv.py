import pandas as pd
import os
import shutil

# 读取两个CSV文件
df1 = pd.read_csv('best.csv')
df2 = pd.read_csv('test_results.csv')

# 合并两个DataFrame并找出label不同的行
merged = pd.merge(df1, df2, on='ImageID', suffixes=('_1', '_2'))
different_labels = merged[merged['label_1'] != merged['label_2']]

# 设置路径
original_images_dir = r'D:\zhongyao\test_set'
new_folder = r'D:\zhongyao\diff'
os.makedirs(new_folder, exist_ok=True)

# 复制图片
for image_id in different_labels['ImageID']:
    # 移除可能存在的.jpg后缀，然后统一添加一次
    image_id = image_id.replace('.jpg', '')
    src = os.path.join(original_images_dir, f"{image_id}.jpg")
    dst = os.path.join(new_folder, f"{image_id}.jpg")
    try:
        shutil.copy2(src, dst)
    except FileNotFoundError:
        print(f"找不到图片: {src}")