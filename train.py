from ultralytics import YOLO

if __name__ == '__main__':
    # 加载 YOLOv8 预训练模型，你可以根据需求选择不同大小的模型
    # 例如 yolov8n-cls.pt（ Nano 版本，轻量）、yolov8s-cls.pt（ Small 版本）、yolov8x-cls.pt（ Extra-large 版本）
    model = YOLO('yolo11x-cls.pt')  

    # 训练参数
    results = model.train(
        data='dataset',
        epochs=100,  # 增加训练轮次
        imgsz=256,   # 适当增大输入尺寸
        batch=16,    # 降低batch保证显存
        device=0,
        
        # 优化器配置
        optimizer='AdamW',
        lr0=1e-4,     # 更小的初始学习率
        lrf=0.01,     # 余弦退火最终学习率
        weight_decay=1e-4,
        
        # 高级正则化
        label_smoothing=0.1,  # 标签平滑
        dropout=0.3,          # 增加Dropout
        
        # 学习率调度
        warmup_epochs=5,      # 预热5个epoch
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        
        # 增强强化（针对中草药）
        hsv_h=0.4,           # 增强色调扰动
        hsv_s=0.6,           # 增强饱和度扰动
        degrees=45,          # 更大旋转角度
        shear=20,            # 更大剪切变换
        perspective=0.001,   # 透视变换
        mixup=0.2,           # MixUp增强
        mosaic=1.0,          # 使用mosaic增强
        
        # 模型保存
        save_period=10,
        name='herb_cls_enhanced'
    )