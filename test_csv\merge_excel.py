import pandas as pd

# 配置参数 - 请根据实际情况修改
first_excel_path = "中草药识别结果_数据清洗后.xlsx"  # 第一个Excel文件路径
second_excel_path = "中草药识别结果_20250807_220256.xlsx"  # 第二个Excel文件路径
output_excel_path = "合并结果.xlsx"  # 输出文件路径
image_name_column = "图片名称"  # 图片名称列名
recognition_result_column = "识别结果"  # 识别结果列名

# 读取两个Excel文件
df1 = pd.read_excel(first_excel_path)
df2 = pd.read_excel(second_excel_path)

# 创建从图片名称到识别结果的映射字典
update_dict = df2.set_index(image_name_column)[recognition_result_column].to_dict()

# 更新第一个表中的识别结果列
df1[recognition_result_column] = df1[image_name_column].map(update_dict).fillna(df1[recognition_result_column])

# 保存结果
df1.to_excel(output_excel_path, index=False)

print(f"合并完成，结果已保存至: {output_excel_path}")