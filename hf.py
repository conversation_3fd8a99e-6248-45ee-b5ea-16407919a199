
from sklearn.model_selection import train_test_split
import os, shutil

dataset_path = "train_set"
train_path = "dataset/train"
val_path = "dataset/val"

for cls in os.listdir(dataset_path):
    imgs = [f for f in os.listdir(os.path.join(dataset_path, cls)) if f.endswith(('.jpg','.png'))]
    train_imgs, val_imgs = train_test_split(imgs, test_size=0.2, random_state=42)
    
    # 复制训练集
    os.makedirs(os.path.join(train_path, cls), exist_ok=True)
    for img in train_imgs:
        shutil.copy(os.path.join(dataset_path, cls, img), os.path.join(train_path, cls, img))
        
    # 复制验证集
    os.makedirs(os.path.join(val_path, cls), exist_ok=True)
    for img in val_imgs:
        shutil.copy(os.path.join(dataset_path, cls, img), os.path.join(val_path, cls, img))