import os
import csv
import torch
from torchvision import transforms
from torch.utils.data import Dataset, DataLoader
from ultralytics import YOLO
from PIL import Image
from collections import Counter

# 分类和标签映射
CLASS_NAMES = [
    '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', 'Chenopodiumalbu<PERSON>', 'Commelina_communis', 'Dandel<PERSON>',
    'Denglongcao', 'Eichhorniacrassipes', '<PERSON><PERSON><PERSON>', 'Garden<PERSON>', 'Ginsengs',
    'Gouweibacao', 'Guizhencao', 'Hairyveinagrimony', '<PERSON><PERSON><PERSON><PERSON>', 'Honeysuckles',
    '<PERSON><PERSON><PERSON>', '<PERSON>huaca<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>seed', '<PERSON><PERSON>',
    'Mangnoliaofficinalis', 'Mantu<PERSON>uo', 'Moneygrass', 'Monochoriavaginalis',
    'Morningglory', 'Odoratum', 'Ophiopogon', 'Palms', 'Perillas', '<PERSON>llia',
    'Plantains', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Radi<PERSON>satid<PERSON>', '<PERSON>du<PERSON>_sarmentosum',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Tongquancao', '<PERSON>ahlenbergia',
    '<PERSON>ormwood', '<PERSON>ji', '<PERSON><PERSON>eyi', '<PERSON>nma', '<PERSON>chuan<PERSON>', 'Zeqi',
    'Zhajiangcao', 'Ziyunying'
]

label_to_category = {
    "<PERSON>suckles": 0, "<PERSON>ia": 1, "Tianhukui": 2, "Gouweibacao": 3, "Shuiqincai": 4,
    "Morningglory": 5, "Bosipopona": 6, "Mantuoluo": 7, "Tongquancao": 8, "Perillas": 9,
    "Jicai": 10, "Xiaoji": 11, "Angelica": 12, "Heshouwu": 13, "Yichuanhong": 14,
    "Malan": 15, "Rabdosiaserra": 16, "Zeqi": 17, "Bupleurum": 18, "Plantains": 19,
    "Ginsengs": 20, "Juaner": 21, "Kucai": 22, "Selfheals": 23, "Sedum_sarmentosum": 24,
    "Agastacherugosa": 25, "Xunma": 26, "Boheye": 27, "Hairyveinagrimony": 28, "Feipeng": 29,
    "Guizhencao": 30, "Eichhorniacrassipes": 31, "Dandelions": 32, "Zhajiangcao": 33,
    "Wahlenbergia": 34, "Radixisatidis": 35, "Mangnoliaofficinalis": 36, "Odoratum": 37,
    "Cangerzi": 38, "Commelina_communis": 39, "Chenopodiumalbum": 40, "Monochoriavaginalis": 41,
    "Ziyunying": 42, "Pinellia": 43, "Hongliao": 44, "Moneygrass": 45, "Lotusseed": 46,
    "Ophiopogon": 47, "Qigucao": 48, "Huanghuacai": 49, "Wormwood": 50, "Palms": 51,
    "Denglongcao": 52, "Xiaoqieyi": 53
}

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 加载 EfficientNet-B4 模型
try:
    from efficientnet_pytorch import EfficientNet
except ImportError:
    from efficientnet_pytorch import EfficientNet

num_classes = 54
efficientnet_model = EfficientNet.from_name('efficientnet-b4', num_classes=num_classes)
efficientnet_model = efficientnet_model.to(device)
efficientnet_model.load_state_dict(torch.load('v2.pth', map_location=device))
efficientnet_model.eval()
print("EfficientNet 模型已加载并设置为评估模式。")

# 测试集的数据预处理
test_transform = transforms.Compose([
    transforms.Resize(300 + 32),
    transforms.CenterCrop(300),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# 自定义测试集 Dataset
class TestDataset(Dataset):
    def __init__(self, root, transform=None):
        self.root = root
        self.transform = transform
        self.image_ids = [f for f in os.listdir(root) if os.path.isfile(os.path.join(root, f))]

    def __len__(self):
        return len(self.image_ids)

    def __getitem__(self, index):
        img_path = os.path.join(self.root, self.image_ids[index])
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return img_path, image

# 使用 EfficientNet-B4 进行预测，返回类别和概率
def predict_with_efficientnet(model, device, image):
    with torch.no_grad():
        image = image.unsqueeze(0).to(device)
        outputs = model(image)
        probs = torch.nn.functional.softmax(outputs, dim=1)
        top_prob, top_index = torch.max(probs, 1)
        predicted_category = CLASS_NAMES[top_index.item()]
        return predicted_category, top_prob.item()

# 使用 YOLO 进行预测，返回类别和概率
def predict_with_yolo(model, image_path):
    try:
        results = model(image_path)
        predicted_category = results[0].names[results[0].probs.top1]
        prob = results[0].probs.top1conf.item()
        return predicted_category, prob
    except Exception as e:
        print(f"YOLO 处理文件 {image_path} 时出错: {e}")
        return None, None

if __name__ == '__main__':
    import torch.multiprocessing as mp
    mp.freeze_support()

    # 测试集路径
    TEST_DIR = r"test_set"
    # 预训练 YOLO 模型路径
    yolo_model_path = r'best.pt'
    yolo_model = YOLO(yolo_model_path)

    test_dataset = TestDataset(root=TEST_DIR, transform=test_transform)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False, num_workers=4, pin_memory=True)

    predictions = []
    with torch.no_grad():
        for img_path, images in test_loader:
            for single_path, single_image in zip(img_path, images):
                # 使用 EfficientNet 预测
                eff_category, eff_prob = predict_with_efficientnet(efficientnet_model, device, single_image)
                # 使用 YOLO 预测
                yolo_category, yolo_prob = predict_with_yolo(yolo_model, single_path)

                all_results = []
                if eff_category is not None:
                    all_results.append(eff_category)
                if yolo_category is not None:
                    all_results.append(yolo_category)

                category_counts = Counter(all_results)

                if len(category_counts) > 1:
                    # 有分歧，再次使用 EfficientNet 检测
                    new_eff_category, _ = predict_with_efficientnet(efficientnet_model, device, single_image)
                    if new_eff_category is not None:
                        all_results.append(new_eff_category)
                        category_counts = Counter(all_results)

                max_count = max(category_counts.values())
                most_common_categories = [
                    category for category, count in category_counts.items() if count == max_count
                ]

                if len(most_common_categories) == 1:
                    final_category = most_common_categories[0]
                else:
                    # 若概率都一样，保留最后一次的测试结果
                    final_category = all_results[-1]
             
                label = label_to_category[final_category]
                predictions.append([os.path.basename(single_path), label])

    # 写入 CSV 文件
    csv_file = 'test_results.csv'
    with open(csv_file, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['ImageID', 'label'])
        writer.writerows(predictions)

    print(f"预测完成，结果已保存到 {csv_file}")