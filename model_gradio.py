# -*- coding: utf-8 -*-
import os
import gradio as gr
import base64
from zhipuai import ZhipuAI
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
from datetime import datetime
import tempfile
from pathlib import Path

# 图片文字识别功能
DEFAULT_API_KEY = "a95934598bc145328b2a1170e251242e.w5JWPbRPrER3cLYW"
DEFAULT_PROMPT = """
- Role: 专业且权威的中草药识别专家
- Background: 用户需要对中草药进行精准识别，通过提供图片的方式，希望从给定的中草药列表中筛选出最契合图片特征的中药。
- Profile: 你是一位在中草药领域拥有深厚且全面知识体系的专家，能够精准无误地识别各类中草药，具备丰富的实践经验。
- Skills: 你具备敏锐的观察力和专业的鉴别能力，能够仔细观察中草药图片的形状、颜色、纹理、生长形态等细节，并依据这些细节从给定列表中筛选出最符合条件的中药。
- Goals: 根据用户提供的中草药图片，从给定列表中迅速且准确地筛选出最契合图片特征的中药。
- Constrains: 交流内容仅围绕中草药识别相关，不回答与中草药识别无关的问题；输出内容仅为最符合条件那个中药的名称，无其他额外内容；输出的名称必定是列表中的一个；严格按要求输出。
- OutputFormat: 输出最符合条件的中药名称，名称必须来自列表。
- Workflow:
  1. 接收用户提供的中草药图片。
  2. 仔细观察图片中中草药的形状、颜色、纹理、生长形态等细节。
  3. 依据观察到的细节，从给定列表中筛选出最契合图片特征的中药。

## 中草药列表：
 [
    'Agastacherugosa', 'Angelica', 'Boheye', 'Bosipopona', 'Bupleurum',
    'Cangerzi', 'Chenopodiumalbum', 'Commelina_communis', 'Dandelions',
    'Denglongcao', 'Eichhorniacrassipes', 'Feipeng', 'Gardenia', 'Ginsengs',
    'Gouweibacao', 'Guizhencao', 'Hairyveinagrimony', 'Heshouwu', 'Honeysuckles',
    'Hongliao', 'Huanghuacai', 'Jicai', 'Juaner', 'Kucai', 'Lotusseed', 'Malan',
    'Mangnoliaofficinalis', 'Mantuoluo', 'Moneygrass', 'Monochoriavaginalis',
    'Morningglory', 'Odoratum', 'Ophiopogon', 'Palms', 'Perillas', 'Pinellia',
    'Plantains', 'Qigucao', 'Rabdosiaserra', 'Radixisatidis', 'Sedum_sarmentosum',
    'Selfheals', 'Shuiqincai', 'Tianhukui', 'Tongquancao', 'Wahlenbergia',
    'Wormwood', 'Xiaoji', 'Xiaoqieyi', 'Xunma', 'Yichuanhong', 'Zeqi',
    'Zhajiangcao', 'Ziyunying'
]
"""

def extract_text_from_image(img_source, api_key, model_name, prompt):
    """兼容文件路径和文件对象的通用处理方法"""
    # 确保获取到有效的本地路径
    if isinstance(img_source, str):
        img_path = img_source
    elif hasattr(img_source, 'read'):
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            tmp_file.write(img_source.read())
            img_path = tmp_file.name
    else:
        raise ValueError("不支持的图片源格式")
    with open(img_path, 'rb') as img_file:
        img_base = base64.b64encode(img_file.read()).decode('utf-8')

    client = ZhipuAI(api_key=api_key)
    response = client.chat.completions.create(
        model=model_name,
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{img_base}"
                        }
                    },
                    {
                        "type": "text",
                        "text": prompt
                    }
                ]
            }
        ]
    )
    return os.path.basename(img_path), response.choices[0].message.content

def process_single_image(image_file, api_key, model_name, prompt):
    """处理单张图片"""
    try:
        _, result = extract_text_from_image(image_file.name, api_key, model_name, prompt)
        return result
    except Exception as e:
        return f"处理图片时出错: {str(e)}"

def process_multiple_images(image_sources, api_key, model_name, prompt):
    """增强版：支持混合类型的输入源（路径/文件对象）"""
    results = []
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(extract_text_from_image, src, api_key, model_name, prompt) 
                  for src in image_sources]
        for future in futures:
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append(f"处理图片时出错: {str(e)}")
    return results

def process_multiple_images(image_sources, api_key, model_name, prompt, progress=gr.Progress(), stop_event=None):
    """增强版：支持混合类型的输入源（路径/文件对象）"""
    results = []
    total = len(image_sources)
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(extract_text_from_image, src, api_key, model_name, prompt) 
                  for src in image_sources]
        for i, future in enumerate(futures):
            if stop_event and stop_event.is_set():
                return results
            try:
                progress((i+1)/total, desc=f"处理中 ({i+1}/{total})")
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append(f"处理图片时出错: {str(e)}")
    return results
def save_results_to_excel(results):
    """将结果保存到Excel文件"""
    # 处理不同格式的results
    processed_results = []
    for item in results:
        if isinstance(item, tuple) and len(item) == 2:
            name, result = item
            processed_results.append((os.path.splitext(name)[0], result))
        else:
            # 处理错误信息或单个结果
            processed_results.append(('未知图片', item))
            
    df = pd.DataFrame(processed_results, columns=['图片名称', '识别结果'])

    # 生成文件名（包含时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"中草药识别结果_{timestamp}.xlsx"

    # 保存到Excel
    df.to_excel(filename, index=False, engine='openpyxl')

    return filename

def toggle_upload_type(upload_type):
    """根据上传类型切换显示的组件"""
    if upload_type == "图片上传":
        return gr.update(visible=True), gr.update(visible=False)
    else:
        return gr.update(visible=False), gr.update(visible=True)

def update_upload_status(files):
    """更新上传文件状态显示"""
    if files is None or len(files) == 0:
        return "### 上传文件列表 (0 张图片)", gr.update(open=False)

    # 统计图片文件数量
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}
    image_count = 0

    for file in files:
        if hasattr(file, 'name'):
            file_ext = os.path.splitext(file.name)[1].lower()
            if file_ext in image_extensions:
                image_count += 1
        else:
            # 如果没有name属性，假设是图片文件
            image_count += 1

    status_text = f"### 上传文件列表 ({image_count} 张图片)"

    # 如果上传了文件，自动折叠手风琴
    accordion_state = gr.update(open=False) if image_count > 0 else gr.update(open=True)

    return status_text, accordion_state

# ... 已有代码 ...

# Gradio 界面
with gr.Blocks() as ocr_ui:
    gr.Markdown("# 中药识别大模型")
    with gr.Group():
        api_key_input = gr.Textbox(label="API Key", placeholder="请输入您的 API Key", type="password", value=DEFAULT_API_KEY)
        progress_bar = gr.Progress()
        upload_type = gr.Dropdown(
            choices=["图片上传", "文件夹上传"],
            value="图片上传",
            label="选择上传类型"
        )

        # 动态显示上传文件数量的标题
        upload_status = gr.Markdown("### 上传文件列表 (0 张图片)")

        with gr.Accordion("上传文件列表", open=False) as upload_accordion:
            file_input = gr.File(file_types=["image"], label="批量上传图片", file_count="multiple", visible=True)
            folder_input = gr.File(label="上传文件夹", file_count="directory", visible=False)

        model_selector = gr.Dropdown(
            choices=["GLM-4.1V-Thinking-Flash", "GLM-4V-Flash", "glm-4v-plus-0111"],
            value="GLM-4.1V-Thinking-Flash",
            label="选择模型"
        )
        prompt_input = gr.Textbox(label="自定义提示词", value=DEFAULT_PROMPT, lines=10)
        text_output = gr.Textbox(label="识别结果", lines=10, interactive=False)
        output_file = gr.File(label="下载识别结果 Excel", interactive=False)
        with gr.Row():
            submit_button = gr.Button("提交", variant="primary")
            stop_button = gr.Button("停止处理", variant="stop")

    upload_type.change(
        fn=toggle_upload_type,  # 切换上传组件的可见性
        inputs=upload_type,
        outputs=[file_input, folder_input]  # 切换上传组件的可见性
    )

    file_input.change(
        fn=update_upload_status,  # 更新上传文件状态显示
        inputs=file_input,
        outputs=[upload_status, upload_accordion]
    )

    folder_input.change(
        fn=update_upload_status,  # 更新上传文件状态显示
        inputs=folder_input,
        outputs=[upload_status, upload_accordion]
    )

    # Gradio回调更新
    stop_event = gr.State(False)
    
    # 修改停止按钮的回调函数
    def stop_processing(stop_state):
        stop_state = True
        # 返回重置后的初始状态
        return (
            stop_state,
            "",  # 清空文本输出
            None,  # 清空文件输出
            gr.update(value=None),  # 重置文件输入
            gr.update(value=None)  # 重置文件夹输入
        )
    
    stop_button.click(
        fn=stop_processing,
        inputs=stop_event,
        outputs=[stop_event, text_output, output_file, file_input, folder_input]
    )
    
    def combined_inputs(upload_type, file_input, folder_input, api_key, model_name, prompt, stop_state):
        if stop_state:
            return "处理已停止", None
        if upload_type == "图片上传" and file_input:
            if len(file_input) == 1:
                result = process_single_image(file_input[0], api_key, model_name, prompt)
                return f"上传成功！{result}", None
            else:
                results = process_multiple_images(file_input, api_key, model_name, prompt, stop_event=stop_state)
                excel_file = save_results_to_excel(results)
                return f"上传成功！已处理 {len(results)} 张图片，结果已保存到Excel文件", excel_file
        elif upload_type == "文件夹上传" and folder_input:
            folder_path = os.path.commonpath([file.name for file in folder_input])
            results = process_multiple_images(folder_path, api_key, model_name, prompt, stop_event=stop_state)
            excel_file = save_results_to_excel(results)
            return f"上传成功！已处理 {len(results)} 张图片，结果已保存到Excel文件", excel_file
        else:
            return "请上传图片文件", None
        
    submit_button.click(
        fn=combined_inputs,
        inputs=[upload_type, file_input, folder_input, api_key_input, model_selector, prompt_input],
        outputs=[text_output, output_file],
        show_progress=True
    )
if __name__ == "__main__":
    print("正在启动Gradio界面...")
    try:
        ocr_ui.launch(share=False, server_name="127.0.0.1", server_port=7862)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
