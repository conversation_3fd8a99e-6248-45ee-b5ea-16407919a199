dataset （yolo训练划分的数据集）
diff （两次最终结果对比后，预测结果不一样的测试图片）
runs（yolo训练的结果）
test_set（测试集）
train_set (训练集)
best.csv (测试集最好的结果)
best.pt (yolo训练的权重)
merge_csv.py （前后测试结果对比）
model_gradio.py （构建大模型图片批量识别平台）
test_results.csv （efficientnet-b4和yolo联合测试结果）
test.py（efficientnet-b4和yolo联合测试结果）
train.py（yolo模型训练）
v4.pth (efficientnet-b4权重)
yolo11s-cls.pt（yolo模型初始权重）
yolo11x-cls.pt（yolo模型初始权重）
efficent_model（efficent模型训练好的权重）
hf.py（划分数据集）
merge_excel.py（合并两次大模型不同结果）
zhuanhuan.py（将大模型结果转换成标签csv文件）