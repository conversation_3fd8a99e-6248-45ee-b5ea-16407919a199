import pandas as pd
import os
import shutil
import os
# 分类标签映射字典
category_label_mapping = {
    "Honeysuckles": 0, "<PERSON><PERSON>": 1, "<PERSON><PERSON><PERSON><PERSON><PERSON>": 2, "<PERSON><PERSON>weibacao": 3, "<PERSON>iq<PERSON><PERSON><PERSON>": 4,
    "Morningglory": 5, "<PERSON><PERSON><PERSON><PERSON>": 6, "<PERSON><PERSON><PERSON><PERSON>": 7, "<PERSON><PERSON><PERSON><PERSON>": 8, "<PERSON><PERSON><PERSON>": 9,
    "<PERSON><PERSON><PERSON>": 10, "<PERSON><PERSON>": 11, "<PERSON>": 12, "<PERSON><PERSON><PERSON><PERSON>": 13, "<PERSON><PERSON><PERSON><PERSON>": 14,
    "<PERSON><PERSON>": 15, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>": 16, "<PERSON><PERSON><PERSON>": 17, "<PERSON>up<PERSON>uru<PERSON>": 18, "Plantains": 19,
    "Ginsengs": 20, "<PERSON><PERSON>": 21, "<PERSON><PERSON><PERSON>": 22, "Selfheals": 23, "Sedum_sarmentosum": 24,
    "<PERSON><PERSON><PERSON><PERSON>ug<PERSON>": 25, "<PERSON><PERSON><PERSON>": 26, "<PERSON><PERSON><PERSON>": 27, "Hairyveinagrim<PERSON>": 28, "<PERSON><PERSON><PERSON>": 29,
    "<PERSON>uizhen<PERSON>o": 30, "<PERSON><PERSON><PERSON>iac<PERSON>ipes": 31, "Dandel<PERSON>": 32, "Zha<PERSON>cao": 33,
    "<PERSON><PERSON><PERSON><PERSON>": 34, "<PERSON><PERSON><PERSON><PERSON>id<PERSON>": 35, "<PERSON>gnoliaoffici<PERSON><PERSON>": 36, "<PERSON>dor<PERSON><PERSON>": 37,
    "<PERSON><PERSON><PERSON>": 38, "<PERSON><PERSON><PERSON>_commu<PERSON>": 39, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>": 40, "Mon<PERSON><PERSON><PERSON><PERSON><PERSON>": 41,
    "<PERSON><PERSON><PERSON><PERSON>": 42, "<PERSON><PERSON><PERSON>": 43, "<PERSON><PERSON><PERSON>": 44, "<PERSON>grass": 45, "<PERSON><PERSON>d": 46,
    "<PERSON><PERSON><PERSON><PERSON><PERSON>": 47, "<PERSON><PERSON><PERSON><PERSON>": 48, "<PERSON><PERSON><PERSON><PERSON>": 49, "<PERSON><PERSON><PERSON>": 50, "<PERSON><PERSON>": 51,
    "<PERSON><PERSON><PERSON><PERSON><PERSON>": 52, "Xiaoqieyi": 53
}

# 配置参数 - 请根据实际情况修改以下路径和列名
input_excel_path = r"D:\zhongyao\test_csv\合并结果.xlsx"  # 输入Excel文件路径
output_csv_path = r"D:\zhongyao\test_csv\result.csv"  # 输出CSV文件路径
image_name_column = "图片名称"  # Excel中图片名称所在列的列名
recognition_result_column = "识别结果"  # Excel中识别结果所在列的列名
original_images_dir = r"D:\zhongyao\test_set"  # 新增：原始图片所在目录


# 读取Excel文件
df = pd.read_excel(input_excel_path)

# 提取ImageID（去除文件名扩展名）
df['ImageID'] = df[image_name_column].apply(lambda x: os.path.splitext(os.path.basename(x))[0])


# 映射识别结果到标签数字并转换为整型
df['label'] = df[recognition_result_column].astype(str).map(category_label_mapping)


# 创建存放未匹配图片的文件夹
output_false_images_dir = os.path.join(os.path.dirname(output_csv_path), "false_images")
os.makedirs(output_false_images_dir, exist_ok=True)

# 打印未匹配的图片名称并复制到新文件夹
# 打印未匹配的图片名称并复制到新文件夹
unmatched_images = df[df['label'].isna()][image_name_column]
if not unmatched_images.empty:
    print(f"\n以下{len(unmatched_images)}张图片未能匹配到分类标签:")
    for img_name in unmatched_images:
        try:
            # 修改：构建完整图片路径
            img_path = os.path.join(original_images_dir, f"{img_name}.jpg")
            shutil.copy2(img_path, output_false_images_dir)
        except FileNotFoundError:
            print(f"警告：文件 {img_path} 未找到，跳过复制")

print(f"\n未匹配图片已复制到: {output_false_images_dir}")



# 选择需要的列并保存为CSV
result_df = df[['ImageID', 'label']]

# 选择需要的列并保存为CSV
result_df = df[['ImageID', 'label']]
result_df.to_csv(output_csv_path, index=False)

print(f"转换完成！CSV文件已保存至: {output_csv_path}")