import os
import csv
from ultralytics import YOLO
import torch
from torchvision import transforms, models
from PIL import Image

# 分类和标签映射
category_label_mapping = {
    "Honeysuckles": 0, "<PERSON><PERSON>": 1, "<PERSON><PERSON><PERSON><PERSON><PERSON>": 2, "<PERSON><PERSON>weibaca<PERSON>": 3, "<PERSON>iq<PERSON><PERSON><PERSON>": 4,
    "<PERSON>glory": 5, "<PERSON><PERSON><PERSON><PERSON>": 6, "<PERSON><PERSON><PERSON><PERSON>": 7, "<PERSON><PERSON><PERSON><PERSON>": 8, "<PERSON><PERSON><PERSON>": 9,
    "<PERSON><PERSON><PERSON>": 10, "<PERSON><PERSON>": 11, "<PERSON>": 12, "<PERSON><PERSON><PERSON><PERSON>": 13, "<PERSON><PERSON><PERSON><PERSON>": 14,
    "<PERSON><PERSON>": 15, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>": 16, "<PERSON><PERSON><PERSON>": 17, "<PERSON><PERSON><PERSON>uru<PERSON>": 18, "Plantains": 19,
    "Ginsengs": 20, "<PERSON><PERSON>": 21, "<PERSON><PERSON><PERSON>": 22, "Selfheals": 23, "Sedum_sarmentosum": 24,
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>": 25, "<PERSON><PERSON><PERSON>": 26, "<PERSON><PERSON><PERSON>": 27, "<PERSON>y<PERSON>ina<PERSON><PERSON>": 28, "<PERSON><PERSON><PERSON>": 29,
    "<PERSON>uizhencao": 30, "<PERSON><PERSON><PERSON><PERSON><PERSON>ipes": 31, "Dandelions": 32, "Zhajiangcao": 33,
    "<PERSON><PERSON><PERSON><PERSON>": 34, "Ra<PERSON><PERSON><PERSON>id<PERSON>": 35, "<PERSON>gnoliaoffici<PERSON><PERSON>": 36, "<PERSON>dor<PERSON>um": 37,
    "<PERSON><PERSON><PERSON>": 38, "<PERSON><PERSON><PERSON>_com<PERSON><PERSON>": 39, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>": 40, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>": 41,
    "<PERSON><PERSON><PERSON><PERSON>": 42, "<PERSON><PERSON><PERSON>": 43, "<PERSON><PERSON><PERSON>": 44, "<PERSON><PERSON>": 45, "<PERSON>seed": 46,
    "<PERSON><PERSON><PERSON><PERSON><PERSON>": 47, "<PERSON><PERSON><PERSON><PERSON>": 48, "<PERSON><PERSON><PERSON><PERSON>": 49, "<PERSON><PERSON><PERSON>": 50, "Palms": 51,
    "Denglongcao": 52, "Xiaoqieyi": 53
}

# 加载 EfficientNet-B5 模型
def load_efficientnet():
    # 初始化 EfficientNet-B5 模型结构
    model = models.efficientnet_b4(pretrained=False)
    # 本地权重文件路径，需根据实际情况修改
    local_weights_path = 'v4.pth'
    try:
        # 加载本地权重
        state_dict = torch.load(local_weights_path)
        model.load_state_dict(state_dict)
        print(f"成功加载本地 EfficientNet-B5 权重文件: {local_weights_path}")
    except Exception as e:
        print(f"加载本地权重文件 {local_weights_path} 失败: {e}")
    model.eval()
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    return model, device

# 使用 EfficientNet-B5 进行预测
def predict_with_efficientnet(model, device, image_path):
    transform = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    try:
        img = Image.open(image_path).convert('RGB')
        img = transform(img).unsqueeze(0).to(device)
        with torch.no_grad():
            outputs = model(img)
            _, predicted = torch.max(outputs.data, 1)
            # 这里假设类别标签和原模型一致，实际可能需要调整
            predicted_category = list(category_label_mapping.keys())[predicted.item()]
            return predicted_category
    except Exception as e:
        print(f"EfficientNet 处理文件 {image_path} 时出错: {e}")
        return None

def classify_images(image_folder, model_path, output_csv):
    # 加载预训练模型
    model = YOLO(model_path)
    # 加载 EfficientNet-B5 模型
    efficientnet_model, device = load_efficientnet()

    # 保留 CSV 文件后缀
    with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['ImageID', 'label']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # 写入 CSV 文件头
        writer.writeheader()

        # 遍历图片文件夹
        for filename in os.listdir(image_folder):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_path = os.path.join(image_folder, filename)
                # 检查文件是否存在
                if not os.path.isfile(image_path):
                    print(f"文件 {image_path} 不存在，跳过。")
                    continue
                try:
                    # 进行图片分类
                    results = model(image_path)
                    # 获取预测的类别名称
                    predicted_category = results[0].names[results[0].probs.top1]
                    # 获取对应的标签值
                    label = category_label_mapping.get(predicted_category, -1)
                except Exception as e:
                    print(f"YOLO 处理文件 {image_path} 时出错: {e}，尝试使用 EfficientNet 处理")
                    predicted_category = predict_with_efficientnet(efficientnet_model, device, image_path)
                    label = category_label_mapping.get(predicted_category, -1) if predicted_category else -1

                # 去掉文件名后缀
                image_id = os.path.splitext(filename)[0]
                # 写入 CSV 文件
                writer.writerow({'ImageID': image_id, 'label': label})



if __name__ == '__main__':
    # 图片文件夹路径
    image_folder = r'test_set'
    # 预训练模型路径
    model_path = r'best.pt'
    # 输出 CSV 文件路径
    output_csv = 'output.csv'

    classify_images(image_folder, model_path, output_csv)